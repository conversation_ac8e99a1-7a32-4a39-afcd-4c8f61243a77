/**
 * 弹窗状态管理服务
 * 统一管理弹窗状态的开关逻辑，提供通用的弹窗状态管理方法
 * 保持纯函数特性，不维护状态，只提供状态计算逻辑
 */

/**
 * 弹窗类型枚举
 */
const POPUP_TYPES = {
  OPTIONS: 'showOptionsPopup',
  TAG: 'showTagPopup',
  MODAL: 'showModal',
}

/**
 * 显示指定类型的弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {string} popupType - 弹窗类型
 * @param {Object} additionalData - 额外需要设置的数据
 */
function showPopup(pageContext, popupType, additionalData = {}) {
  if (!pageContext || typeof pageContext.setData !== 'function') {
    console.error('showPopup: 无效的页面上下文')
    return
  }

  if (!POPUP_TYPES[popupType] && !popupType.startsWith('show')) {
    console.error('showPopup: 无效的弹窗类型', popupType)
    return
  }

  const stateKey = POPUP_TYPES[popupType] || popupType
  const updateData = {
    [stateKey]: true,
    ...additionalData
  }

  pageContext.setData(updateData)
}

/**
 * 隐藏指定类型的弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {string} popupType - 弹窗类型
 * @param {Object} additionalData - 额外需要设置的数据
 */
function hidePopup(pageContext, popupType, additionalData = {}) {
  if (!pageContext || typeof pageContext.setData !== 'function') {
    console.error('hidePopup: 无效的页面上下文')
    return
  }

  if (!POPUP_TYPES[popupType] && !popupType.startsWith('show')) {
    console.error('hidePopup: 无效的弹窗类型', popupType)
    return
  }

  const stateKey = POPUP_TYPES[popupType] || popupType
  const updateData = {
    [stateKey]: false,
    ...additionalData
  }

  pageContext.setData(updateData)
}

/**
 * 显示职位操作选项弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {Object} jobInfo - 职位信息
 * @param {Array} optionsData - 操作选项数据
 */
function showJobOptionsPopup(pageContext, jobInfo, optionsData) {
  showPopup(pageContext, 'OPTIONS', {
    currentJobInfo: jobInfo,
    optionsData: optionsData
  })
}

/**
 * 隐藏职位操作选项弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 */
function hideJobOptionsPopup(pageContext) {
  hidePopup(pageContext, 'OPTIONS')
}

/**
 * 显示标签选择弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {Array} tagOptionsData - 标签选项数据
 */
function showTagPopup(pageContext, tagOptionsData) {
  showPopup(pageContext, 'TAG', {
    tagOptionsData: tagOptionsData
  })
}

/**
 * 隐藏标签选择弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 */
function hideTagPopup(pageContext) {
  hidePopup(pageContext, 'TAG')
}

/**
 * 切换弹窗状态
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {string} popupType - 弹窗类型
 * @param {Object} additionalData - 额外需要设置的数据
 */
function togglePopup(pageContext, popupType, additionalData = {}) {
  if (!pageContext || typeof pageContext.setData !== 'function') {
    console.error('togglePopup: 无效的页面上下文')
    return
  }

  const stateKey = POPUP_TYPES[popupType] || popupType
  const currentState = pageContext.data[stateKey]
  
  const updateData = {
    [stateKey]: !currentState,
    ...additionalData
  }

  pageContext.setData(updateData)
}

/**
 * 批量更新弹窗状态
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {Object} popupStates - 弹窗状态对象 { popupType: boolean }
 * @param {Object} additionalData - 额外需要设置的数据
 */
function batchUpdatePopupStates(pageContext, popupStates, additionalData = {}) {
  if (!pageContext || typeof pageContext.setData !== 'function') {
    console.error('batchUpdatePopupStates: 无效的页面上下文')
    return
  }

  const updateData = { ...additionalData }
  
  Object.keys(popupStates).forEach(popupType => {
    const stateKey = POPUP_TYPES[popupType] || popupType
    updateData[stateKey] = popupStates[popupType]
  })

  pageContext.setData(updateData)
}

/**
 * 关闭所有弹窗
 * @param {Object} pageContext - 页面上下文 (this)
 * @param {Array} popupTypes - 要关闭的弹窗类型数组，默认关闭所有
 */
function hideAllPopups(pageContext, popupTypes = Object.keys(POPUP_TYPES)) {
  const popupStates = {}
  popupTypes.forEach(type => {
    popupStates[type] = false
  })
  batchUpdatePopupStates(pageContext, popupStates)
}

// 导出所有方法和常量
module.exports = {
  POPUP_TYPES,
  showPopup,
  hidePopup,
  showJobOptionsPopup,
  hideJobOptionsPopup,
  showTagPopup,
  hideTagPopup,
  togglePopup,
  batchUpdatePopupStates,
  hideAllPopups,
}
