/**
 * 职位操作服务
 * 封装职位操作相关的业务逻辑，包括标签配置处理、操作选项生成等
 * 保持纯函数特性，不维护状态，不处理缓存和setData操作
 */

/**
 * 生成职位操作选项数据
 * @param {Object} jobInfo - 职位信息
 * @param {Array} compareJobIds - 对比列表中的职位ID数组
 * @returns {Array} 操作选项数组
 */
function generateJobActionOptions(jobInfo, compareJobIds = []) {
  const { jobId, follows_tag } = jobInfo
  
  let optionsData = [
    { name: "添加标签", value: "addTag" },
    { name: "加入对比", value: "addToCompare" },
    { name: "取消关注", value: "unfollow" },
  ]

  // 如果已有标签，修改标签文本
  if (follows_tag) {
    optionsData[0].name = "修改标签"
  }

  // 如果已在对比列表中，移除对比选项
  if (compareJobIds.length && compareJobIds.includes(jobId)) {
    optionsData = [
      { name: "添加标签", value: "addTag" },
      { name: "取消关注", value: "unfollow" },
    ]
  }

  return optionsData
}

/**
 * 处理标签配置数据
 * @param {Array} serverTagConfig - 服务器返回的标签配置
 * @param {string} currentFollowsTag - 当前职位的标签
 * @returns {Array} 处理后的标签选项数组
 */
function processTagOptions(serverTagConfig = [], currentFollowsTag = null) {
  if (!Array.isArray(serverTagConfig) || serverTagConfig.length === 0) {
    return []
  }

  return serverTagConfig.map((item) => {
    if (currentFollowsTag === item.key) {
      return {
        name: "取消" + item.name,
        value: item.key,
      }
    } else {
      return {
        name: "加入" + item.name,
        value: item.key,
      }
    }
  })
}

/**
 * 生成取消关注的确认弹窗配置
 * @param {string} jobName - 职位名称
 * @returns {Object} 弹窗配置对象
 */
function generateUnfollowConfirmConfig(jobName) {
  return {
    title: "确认取消关注",
    content: `确定要取消关注"${jobName}"吗？`,
  }
}

/**
 * 生成标签操作的提示文本
 * @param {string} selectedTag - 选中的标签值
 * @param {string} currentTag - 当前职位的标签值
 * @returns {string} 提示文本
 */
function generateTagActionMessage(selectedTag, currentTag) {
  if (selectedTag === currentTag) {
    return "取消成功"
  } else {
    return "加入成功"
  }
}

/**
 * 检查职位是否在对比列表中
 * @param {string} jobId - 职位ID
 * @param {Array} compareJobIds - 对比列表中的职位ID数组
 * @returns {boolean} 是否在对比列表中
 */
function isJobInCompareList(jobId, compareJobIds = []) {
  return compareJobIds.includes(jobId)
}

/**
 * 添加职位到对比列表
 * @param {string} jobId - 职位ID
 * @param {Array} currentCompareJobIds - 当前对比列表
 * @returns {Array} 更新后的对比列表
 */
function addJobToCompareList(jobId, currentCompareJobIds = []) {
  if (!currentCompareJobIds.includes(jobId)) {
    return [...currentCompareJobIds, jobId]
  }
  return currentCompareJobIds
}

/**
 * 根据 activeTabIndex 获取 API 参数
 * @param {number} activeTabIndex - 当前激活的 tab 索引
 * @param {number} subActiveTabIndex - 子 tab 索引
 * @returns {Object} API 参数对象
 */
function getApiParamsByTabIndex(activeTabIndex, subActiveTabIndex = 0) {
  const param = {
    page: 1,
    size: 20,
  }

  if (activeTabIndex === 2) {
    // 浏览足迹
    param.item_type = subActiveTabIndex === 0 ? "article" : "job"
  } else {
    // 关注公告或关注职位
    param.item_type = activeTabIndex === 0 ? "article" : "job"
  }

  return param
}

/**
 * 根据 activeTabIndex 获取对应的 API URL
 * @param {number} activeTabIndex - 当前激活的 tab 索引
 * @returns {string} API URL
 */
function getApiUrlByTabIndex(activeTabIndex) {
  const API = require("@/config/api")
  return activeTabIndex === 2 ? API.getFootPrintList : API.getFollowsList
}

// 导出所有方法
module.exports = {
  generateJobActionOptions,
  processTagOptions,
  generateUnfollowConfirmConfig,
  generateTagActionMessage,
  isJobInCompareList,
  addJobToCompareList,
  getApiParamsByTabIndex,
  getApiUrlByTabIndex,
}
